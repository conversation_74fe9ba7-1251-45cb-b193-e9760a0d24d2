import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/post_service.dart';

/// GetX Controller for managing poll state and voting logic
class PollController extends GetxController {
  static PollController get instance => Get.find();

  final PostService _postService = PostService();

  // Reactive variables for poll state
  final _votingStates = <String, bool>{}.obs; // postId -> isVoting
  final _optimisticVotes = <String, Map<String, int>>{}.obs; // postId -> votes
  final _userVotes = <String, String?>{}.obs; // postId -> userVote

  // Getters
  bool isVoting(String postId) => _votingStates[postId] ?? false;
  Map<String, int>? getOptimisticVotes(String postId) =>
      _optimisticVotes[postId];
  String? getUserVote(String postId) => _userVotes[postId];

  // Safe getters that ensure initialization
  int getVoteCount(String postId, String option) {
    final votes = _optimisticVotes[postId];
    if (votes == null) return 0;
    return votes[option] ?? 0;
  }

  int getTotalVotes(String postId) {
    final votes = _optimisticVotes[postId];
    if (votes == null) return 0;
    return votes.values.fold(0, (total, count) => total + count);
  }

  double getVotePercentage(String postId, String option) {
    final totalVotes = getTotalVotes(postId);
    if (totalVotes == 0) return 0.0;
    final optionVotes = getVoteCount(postId, option);
    return (optionVotes / totalVotes) * 100;
  }

  @override
  void onInit() {
    super.onInit();
    _initializeUserVotes();
  }

  /// Initialize user votes from current posts
  void _initializeUserVotes() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return;

    // We'll initialize poll data when posts are loaded through updatePollData
    // This avoids calling getAllPosts which might not exist or be synchronous
  }

  /// Update poll data when posts are updated
  void updatePollData(Post post) {
    if (!post.hasPoll) return;

    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser != null) {
      _userVotes[post.id] = post.getUserVote(currentUser.uid);
    }

    // Only update optimistic votes if we're not currently voting
    if (!isVoting(post.id)) {
      _optimisticVotes[post.id] = Map<String, int>.from(post.pollVotes);
    }
  }

  /// Vote on a poll with optimistic updates
  Future<void> voteOnPoll(String postId, String option) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null || isVoting(postId)) return;

    // Set voting state
    _votingStates[postId] = true;
    update(); // Trigger UI update

    // Store previous state for rollback
    final previousVote = _userVotes[postId];
    final previousVotes = _optimisticVotes[postId] != null
        ? Map<String, int>.from(_optimisticVotes[postId]!)
        : <String, int>{};

    // Optimistic update
    _performOptimisticUpdate(postId, option, previousVote);
    update(); // Trigger UI update

    try {
      // Perform actual vote
      await _postService.voteOnPoll(postId, option);

      // Vote successful - the real-time listener will update the actual data
    } catch (e) {
      // Rollback optimistic update on error
      _rollbackOptimisticUpdate(postId, previousVote, previousVotes);
      update(); // Trigger UI update

      // Show error message with safe colors
      Get.snackbar(
        'Error',
        'Failed to vote: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: const Color(0xFFD32F2F), // Red color
        colorText: const Color(0xFFFFFFFF), // White color
        duration: const Duration(seconds: 3),
      );

      // Log the error for debugging
      debugPrint('Poll voting error: $e');
    } finally {
      // Clear voting state
      _votingStates[postId] = false;
      update(); // Trigger UI update
    }
  }

  /// Perform optimistic update
  void _performOptimisticUpdate(
      String postId, String option, String? previousVote) {
    final currentVotes = _optimisticVotes[postId] ?? <String, int>{};
    final newVotes = Map<String, int>.from(currentVotes);

    // Remove previous vote if exists
    if (previousVote != null && newVotes.containsKey(previousVote)) {
      newVotes[previousVote] =
          (newVotes[previousVote]! - 1).clamp(0, double.infinity).toInt();
    }

    // Add new vote
    newVotes[option] = (newVotes[option] ?? 0) + 1;

    // Update state
    _optimisticVotes[postId] = newVotes;
    _userVotes[postId] = option;
  }

  /// Rollback optimistic update
  void _rollbackOptimisticUpdate(
      String postId, String? previousVote, Map<String, int> previousVotes) {
    _optimisticVotes[postId] = previousVotes;
    _userVotes[postId] = previousVote;
  }

  /// Check if user has voted on a poll
  bool hasUserVoted(String postId) {
    return _userVotes[postId] != null;
  }

  /// Clear poll data for a specific post
  void clearPollData(String postId) {
    _votingStates.remove(postId);
    _optimisticVotes.remove(postId);
    _userVotes.remove(postId);
  }

  /// Clear all poll data
  void clearAllPollData() {
    _votingStates.clear();
    _optimisticVotes.clear();
    _userVotes.clear();
  }

  @override
  void onClose() {
    clearAllPollData();
    super.onClose();
  }
}
