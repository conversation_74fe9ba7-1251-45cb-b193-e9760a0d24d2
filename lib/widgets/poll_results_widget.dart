import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A simple pie chart widget for displaying poll results
class PollResultsWidget extends StatelessWidget {
  final Map<String, int> votes;
  final String? userVote;
  final double size;

  const PollResultsWidget({
    super.key,
    required this.votes,
    this.userVote,
    this.size = 120,
  });

  @override
  Widget build(BuildContext context) {
    // Safety check for null or empty votes
    if (votes.isEmpty) {
      return _buildEmptyChart();
    }

    final totalVotes = votes.values.fold(0, (sum, count) => sum + count);
    if (totalVotes == 0) {
      return _buildEmptyChart();
    }

    return Column(
      children: [
        SizedBox(
          width: size,
          height: size,
          child: CustomPaint(
            painter: <PERSON><PERSON><PERSON><PERSON>ainter(
              votes: votes,
              userVote: userVote,
              totalVotes: totalVotes,
            ),
          ),
        ),
        const SizedBox(height: 12),
        _buildLegend(totalVotes),
      ],
    );
  }

  Widget _buildEmptyChart() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.grey[200],
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: const Center(
        child: Text(
          'No votes yet',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ),
    );
  }

  Widget _buildLegend(int totalVotes) {
    final options = [
      {
        'key': 'yes',
        'label': 'Yes',
        'color': Colors.green,
        'icon': Icons.thumb_up
      },
      {
        'key': 'no',
        'label': 'No',
        'color': Colors.red,
        'icon': Icons.thumb_down
      },
      {
        'key': 'dont_care',
        'label': "Don't care",
        'color': Colors.orange,
        'icon': Icons.sentiment_neutral
      },
    ];

    return Column(
      children: options.map((option) {
        final key = option['key'] as String;
        final label = option['label'] as String;
        final color = option['color'] as Color;
        final icon = option['icon'] as IconData;
        final count = votes[key] ?? 0;
        final percentage = totalVotes > 0 ? (count / totalVotes * 100) : 0.0;
        final isUserVote = userVote == key;

        return Container(
          margin: const EdgeInsets.symmetric(vertical: 2),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: isUserVote ? color.withOpacity(0.1) : Colors.transparent,
            borderRadius: BorderRadius.circular(4),
            border: isUserVote ? Border.all(color: color, width: 1) : null,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 6),
              Icon(icon, size: 14, color: color),
              const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: isUserVote ? FontWeight.w600 : FontWeight.normal,
                  color: isUserVote ? color : Colors.grey[700],
                ),
              ),
              const SizedBox(width: 4),
              Text(
                '${percentage.toStringAsFixed(0)}%',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(width: 2),
              Text(
                '($count)',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[500],
                ),
              ),
              if (isUserVote) ...[
                const SizedBox(width: 4),
                Icon(
                  Icons.check_circle,
                  size: 12,
                  color: color,
                ),
              ],
            ],
          ),
        );
      }).toList(),
    );
  }
}

/// Custom painter for the pie chart
class PieChartPainter extends CustomPainter {
  final Map<String, int> votes;
  final String? userVote;
  final int totalVotes;

  PieChartPainter({
    required this.votes,
    required this.userVote,
    required this.totalVotes,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2 - 4;

    final colors = {
      'yes': Colors.green,
      'no': Colors.red,
      'dont_care': Colors.orange,
    };

    double startAngle = -math.pi / 2; // Start from top

    for (final entry in votes.entries) {
      final key = entry.key;
      final count = entry.value;
      if (count == 0) continue;

      final sweepAngle = (count / totalVotes) * 2 * math.pi;
      final color = colors[key] ?? Colors.grey;
      final isUserVote = userVote == key;

      final paint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;

      // Draw the pie slice
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );

      // Draw border for user's vote
      if (isUserVote) {
        final borderPaint = Paint()
          ..color = color.withOpacity(0.8)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 3;

        canvas.drawArc(
          Rect.fromCircle(center: center, radius: radius),
          startAngle,
          sweepAngle,
          true,
          borderPaint,
        );
      }

      startAngle += sweepAngle;
    }

    // Draw center circle
    final centerPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius * 0.4, centerPaint);

    // Draw total votes in center
    final textPainter = TextPainter(
      text: TextSpan(
        children: [
          TextSpan(
            text: '$totalVotes\n',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const TextSpan(
            text: 'votes',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey,
            ),
          ),
        ],
      ),
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        center.dx - textPainter.width / 2,
        center.dy - textPainter.height / 2,
      ),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
