import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/post_service.dart';
import '../models/poll_model.dart';

/// A reusable widget for displaying and interacting with polls
class PollWidget extends StatefulWidget {
  final Post post;
  final bool isDetailView;

  const PollWidget({
    super.key,
    required this.post,
    this.isDetailView = false,
  });

  @override
  State<PollWidget> createState() => _PollWidgetState();
}

class _PollWidgetState extends State<PollWidget> {
  final PostService _postService = PostService();
  String? _userVote;
  Map<String, int> _optimisticVotes = {};
  bool _isVoting = false;

  @override
  void initState() {
    super.initState();
    _initializePollData();
  }

  @override
  void didUpdateWidget(PollWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.post.id != widget.post.id ||
        oldWidget.post.pollVotes != widget.post.pollVotes) {
      _initializePollData();
    }
  }

  void _initializePollData() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser != null) {
      _userVote = widget.post.getUserVote(currentUser.uid);
    }
    _optimisticVotes = Map<String, int>.from(widget.post.pollVotes);
  }

  Future<void> _vote(String option) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null || _isVoting) return;

    setState(() {
      _isVoting = true;
    });

    // Optimistic update
    final previousVote = _userVote;
    final previousVotes = Map<String, int>.from(_optimisticVotes);

    setState(() {
      // Remove previous vote if exists
      if (previousVote != null) {
        _optimisticVotes[previousVote] = 
            (_optimisticVotes[previousVote] ?? 0) - 1;
      }
      
      // Add new vote
      _optimisticVotes[option] = (_optimisticVotes[option] ?? 0) + 1;
      _userVote = option;
    });

    try {
      // Perform actual vote
      await _postService.voteOnPoll(widget.post.id, option);
    } catch (e) {
      // Revert optimistic update on error
      setState(() {
        _optimisticVotes = previousVotes;
        _userVote = previousVote;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to vote: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isVoting = false;
        });
      }
    }
  }

  int get _totalVotes {
    return _optimisticVotes.values.fold(0, (sum, votes) => sum + votes);
  }

  double _getVotePercentage(String option) {
    if (_totalVotes == 0) return 0.0;
    final votes = _optimisticVotes[option] ?? 0;
    return (votes / _totalVotes) * 100;
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.post.hasPoll) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.poll, color: Colors.blue[600], size: 16),
              const SizedBox(width: 6),
              Text(
                'Poll',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.blue[700],
                  fontSize: 13,
                ),
              ),
              const Spacer(),
              Text(
                '$_totalVotes vote${_totalVotes != 1 ? 's' : ''}',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildPollOption(
            'yes',
            'Yes',
            Icons.thumb_up,
            Colors.green,
          ),
          const SizedBox(height: 8),
          _buildPollOption(
            'no',
            'No',
            Icons.thumb_down,
            Colors.red,
          ),
          const SizedBox(height: 8),
          _buildPollOption(
            'dont_care',
            "Don't care",
            Icons.sentiment_neutral,
            Colors.orange,
          ),
        ],
      ),
    );
  }

  Widget _buildPollOption(
    String optionKey,
    String optionText,
    IconData icon,
    Color color,
  ) {
    final votes = _optimisticVotes[optionKey] ?? 0;
    final percentage = _getVotePercentage(optionKey);
    final isSelected = _userVote == optionKey;
    final currentUser = FirebaseAuth.instance.currentUser;
    final canVote = currentUser != null && !_isVoting;

    return GestureDetector(
      onTap: canVote ? () => _vote(optionKey) : null,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.white,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Stack(
          children: [
            // Progress bar background
            if (_totalVotes > 0)
              Positioned.fill(
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: percentage / 100,
                  child: Container(
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ),
            // Content
            Row(
              children: [
                Icon(
                  icon,
                  size: 16,
                  color: isSelected ? color : Colors.grey[600],
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    optionText,
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      color: isSelected ? color : Colors.grey[700],
                    ),
                  ),
                ),
                if (_totalVotes > 0) ...[
                  Text(
                    '${percentage.toStringAsFixed(0)}%',
                    style: TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                Text(
                  votes.toString(),
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: isSelected ? color : Colors.grey[600],
                  ),
                ),
                if (isSelected) ...[
                  const SizedBox(width: 4),
                  Icon(
                    Icons.check_circle,
                    size: 14,
                    color: color,
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }
}
